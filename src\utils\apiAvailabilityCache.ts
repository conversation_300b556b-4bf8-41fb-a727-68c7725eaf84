/**
 * API Availability Cache
 * Prevents repeated failed requests to unavailable API endpoints
 */

interface ApiStatus {
  available: boolean;
  lastChecked: number;
  consecutiveFailures: number;
}

class ApiAvailabilityCache {
  private static instance: ApiAvailabilityCache;
  private cache: Map<string, ApiStatus> = new Map();
  private readonly CACHE_DURATION = 5 * 60 * 1000; // 5 minutes
  private readonly MAX_CONSECUTIVE_FAILURES = 3;

  static getInstance(): ApiAvailabilityCache {
    if (!ApiAvailabilityCache.instance) {
      ApiAvailabilityCache.instance = new ApiAvailabilityCache();
    }
    return ApiAvailabilityCache.instance;
  }

  /**
   * Check if an API endpoint is available
   */
  isApiAvailable(endpoint: string): boolean {
    const status = this.cache.get(endpoint);
    
    if (!status) {
      // No cache entry, assume available for first try
      return true;
    }

    const now = Date.now();
    const isExpired = now - status.lastChecked > this.CACHE_DURATION;

    if (isExpired) {
      // Cache expired, allow retry
      this.cache.delete(endpoint);
      return true;
    }

    // Return cached availability status
    return status.available;
  }

  /**
   * Mark an API endpoint as successful
   */
  markApiSuccess(endpoint: string): void {
    this.cache.set(endpoint, {
      available: true,
      lastChecked: Date.now(),
      consecutiveFailures: 0
    });
    
    console.log(`[ApiCache] Marked ${endpoint} as available`);
  }

  /**
   * Mark an API endpoint as failed
   */
  markApiFailure(endpoint: string, statusCode?: number): void {
    const existing = this.cache.get(endpoint);
    const consecutiveFailures = (existing?.consecutiveFailures || 0) + 1;
    
    // For 404 errors, immediately mark as unavailable
    const isUnavailable = statusCode === 404 || consecutiveFailures >= this.MAX_CONSECUTIVE_FAILURES;
    
    this.cache.set(endpoint, {
      available: !isUnavailable,
      lastChecked: Date.now(),
      consecutiveFailures
    });

    if (isUnavailable) {
      console.warn(`[ApiCache] Marked ${endpoint} as unavailable after ${consecutiveFailures} failures (status: ${statusCode})`);
    } else {
      console.warn(`[ApiCache] API failure ${consecutiveFailures}/${this.MAX_CONSECUTIVE_FAILURES} for ${endpoint} (status: ${statusCode})`);
    }
  }

  /**
   * Get the status of an API endpoint
   */
  getApiStatus(endpoint: string): ApiStatus | null {
    return this.cache.get(endpoint) || null;
  }

  /**
   * Clear cache for a specific endpoint
   */
  clearEndpoint(endpoint: string): void {
    this.cache.delete(endpoint);
    console.log(`[ApiCache] Cleared cache for ${endpoint}`);
  }

  /**
   * Clear all cache entries
   */
  clearAll(): void {
    this.cache.clear();
    console.log('[ApiCache] Cleared all cache entries');
  }

  /**
   * Get cache statistics
   */
  getStats(): { endpoint: string; status: ApiStatus }[] {
    return Array.from(this.cache.entries()).map(([endpoint, status]) => ({
      endpoint,
      status
    }));
  }

  /**
   * Check if we should attempt an API call
   */
  shouldAttemptCall(endpoint: string): { shouldCall: boolean; reason: string } {
    if (!this.isApiAvailable(endpoint)) {
      const status = this.getApiStatus(endpoint);
      const timeUntilRetry = status ? 
        Math.max(0, this.CACHE_DURATION - (Date.now() - status.lastChecked)) : 0;
      
      return {
        shouldCall: false,
        reason: `API marked as unavailable. Retry in ${Math.ceil(timeUntilRetry / 1000)}s`
      };
    }

    return {
      shouldCall: true,
      reason: 'API available or not yet tested'
    };
  }
}

// Export singleton instance
export const apiCache = ApiAvailabilityCache.getInstance();

// Export types for external use
export type { ApiStatus };
