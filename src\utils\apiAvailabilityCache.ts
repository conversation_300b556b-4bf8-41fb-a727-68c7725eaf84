/**
 * API Availability Cache
 * Prevents repeated failed requests to unavailable API endpoints
 */

interface ApiStatus {
  available: boolean;
  lastChecked: number;
  consecutiveFailures: number;
}

class ApiAvailabilityCache {
  private static instance: ApiAvailabilityCache;
  private cache: Map<string, ApiStatus> = new Map();
  private pendingCalls: Map<string, Promise<boolean>> = new Map();
  private readonly CACHE_DURATION = 5 * 60 * 1000; // 5 minutes
  private readonly MAX_CONSECUTIVE_FAILURES = 3;
  private readonly instanceId: string;

  constructor() {
    this.instanceId = Math.random().toString(36).substring(2, 11);
    console.log(`[ApiCache] 🏗️ Creating new ApiAvailabilityCache instance: ${this.instanceId}`);
  }

  static getInstance(): ApiAvailabilityCache {
    if (!ApiAvailabilityCache.instance) {
      console.log('[ApiCache] 🆕 Creating singleton instance');
      ApiAvailabilityCache.instance = new ApiAvailabilityCache();

      // Add to global scope for debugging
      if (typeof window !== 'undefined') {
        (window as unknown as { apiCache: ApiAvailabilityCache }).apiCache = ApiAvailabilityCache.instance;
        console.log('[ApiCache] 🌐 Added to window.apiCache for debugging');
      }
    } else {
      console.log(`[ApiCache] ♻️ Returning existing instance: ${ApiAvailabilityCache.instance.instanceId}`);
    }
    return ApiAvailabilityCache.instance;
  }

  /**
   * Check if an API endpoint is available
   */
  isApiAvailable(endpoint: string): boolean {
    const status = this.cache.get(endpoint);

    console.log(`[ApiCache] Checking availability for ${endpoint}:`, {
      hasStatus: !!status,
      status: status ? { ...status, lastChecked: new Date(status.lastChecked).toISOString() } : null,
      cacheSize: this.cache.size
    });

    if (!status) {
      // No cache entry, assume available for first try
      console.log(`[ApiCache] No cache entry for ${endpoint}, allowing first try`);
      return true;
    }

    const now = Date.now();
    const isExpired = now - status.lastChecked > this.CACHE_DURATION;
    const timeRemaining = Math.max(0, this.CACHE_DURATION - (now - status.lastChecked));

    console.log(`[ApiCache] Cache status for ${endpoint}:`, {
      available: status.available,
      isExpired,
      timeRemaining: Math.ceil(timeRemaining / 1000) + 's',
      consecutiveFailures: status.consecutiveFailures
    });

    if (isExpired) {
      // Cache expired, allow retry
      console.log(`[ApiCache] Cache expired for ${endpoint}, clearing and allowing retry`);
      this.cache.delete(endpoint);
      return true;
    }

    // Return cached availability status
    console.log(`[ApiCache] Returning cached status for ${endpoint}: ${status.available}`);
    return status.available;
  }

  /**
   * Mark an API endpoint as successful
   */
  markApiSuccess(endpoint: string): void {
    const newStatus = {
      available: true,
      lastChecked: Date.now(),
      consecutiveFailures: 0
    };

    this.cache.set(endpoint, newStatus);
    this.clearPendingCall(endpoint);

    console.log(`[ApiCache] ✅ Marked ${endpoint} as available:`, {
      instanceId: this.instanceId,
      cacheSize: this.cache.size,
      status: { ...newStatus, lastChecked: new Date(newStatus.lastChecked).toISOString() }
    });
  }

  /**
   * Mark an API endpoint as failed
   */
  markApiFailure(endpoint: string, statusCode?: number): void {
    const existing = this.cache.get(endpoint);
    const consecutiveFailures = (existing?.consecutiveFailures || 0) + 1;

    // For 404 errors, immediately mark as unavailable
    const isUnavailable = statusCode === 404 || consecutiveFailures >= this.MAX_CONSECUTIVE_FAILURES;

    const newStatus = {
      available: !isUnavailable,
      lastChecked: Date.now(),
      consecutiveFailures
    };

    this.cache.set(endpoint, newStatus);
    this.clearPendingCall(endpoint);

    console.log(`[ApiCache] ${isUnavailable ? '❌' : '⚠️'} API failure for ${endpoint}:`, {
      instanceId: this.instanceId,
      statusCode,
      consecutiveFailures,
      maxFailures: this.MAX_CONSECUTIVE_FAILURES,
      isUnavailable,
      cacheSize: this.cache.size,
      status: { ...newStatus, lastChecked: new Date(newStatus.lastChecked).toISOString() }
    });

    if (isUnavailable) {
      console.warn(`[ApiCache] 🚫 ${endpoint} marked as UNAVAILABLE after ${consecutiveFailures} failures (status: ${statusCode})`);
    } else {
      console.warn(`[ApiCache] ⚠️ API failure ${consecutiveFailures}/${this.MAX_CONSECUTIVE_FAILURES} for ${endpoint} (status: ${statusCode})`);
    }
  }

  /**
   * Get the status of an API endpoint
   */
  getApiStatus(endpoint: string): ApiStatus | null {
    return this.cache.get(endpoint) || null;
  }

  /**
   * Clear cache for a specific endpoint
   */
  clearEndpoint(endpoint: string): void {
    this.cache.delete(endpoint);
    console.log(`[ApiCache] Cleared cache for ${endpoint}`);
  }

  /**
   * Clear all cache entries
   */
  clearAll(): void {
    this.cache.clear();
    console.log('[ApiCache] Cleared all cache entries');
  }

  /**
   * Get cache statistics
   */
  getStats(): { endpoint: string; status: ApiStatus }[] {
    return Array.from(this.cache.entries()).map(([endpoint, status]) => ({
      endpoint,
      status
    }));
  }

  /**
   * Get instance information for debugging
   */
  getInstanceInfo(): { instanceId: string; cacheSize: number; cacheEntries: { endpoint: string; status: ApiStatus }[] } {
    return {
      instanceId: this.instanceId,
      cacheSize: this.cache.size,
      cacheEntries: this.getStats()
    };
  }

  /**
   * Log current cache state for debugging
   */
  logCacheState(context: string): void {
    const info = this.getInstanceInfo();
    console.log(`[ApiCache] 📊 Cache state (${context}):`, {
      instanceId: info.instanceId,
      cacheSize: info.cacheSize,
      pendingCalls: this.pendingCalls.size,
      entries: info.cacheEntries.map(entry => ({
        endpoint: entry.endpoint,
        available: entry.status.available,
        failures: entry.status.consecutiveFailures,
        lastChecked: new Date(entry.status.lastChecked).toISOString()
      }))
    });
  }

  /**
   * Check if there's a pending call for this endpoint
   */
  hasPendingCall(endpoint: string): boolean {
    return this.pendingCalls.has(endpoint);
  }

  /**
   * Mark a call as pending
   */
  markCallPending(endpoint: string): void {
    const promise = new Promise<boolean>((resolve) => {
      // This will be resolved when markApiSuccess or markApiFailure is called
      setTimeout(() => resolve(false), 10000); // 10 second timeout
    });
    this.pendingCalls.set(endpoint, promise);
    console.log(`[ApiCache] 🔄 Marked ${endpoint} as pending call`);
  }

  /**
   * Clear pending call
   */
  clearPendingCall(endpoint: string): void {
    this.pendingCalls.delete(endpoint);
    console.log(`[ApiCache] ✅ Cleared pending call for ${endpoint}`);
  }

  /**
   * Check if we should attempt an API call
   */
  shouldAttemptCall(endpoint: string): { shouldCall: boolean; reason: string } {
    console.log(`[ApiCache] 🔍 shouldAttemptCall for ${endpoint} (instance: ${this.instanceId})`);

    // Check if there's already a pending call
    if (this.hasPendingCall(endpoint)) {
      const result = {
        shouldCall: false,
        reason: 'API call already in progress'
      };
      console.log(`[ApiCache] ⏳ Call already pending for ${endpoint}:`, result);
      return result;
    }

    const isAvailable = this.isApiAvailable(endpoint);

    if (!isAvailable) {
      const status = this.getApiStatus(endpoint);
      const timeUntilRetry = status ?
        Math.max(0, this.CACHE_DURATION - (Date.now() - status.lastChecked)) : 0;

      const result = {
        shouldCall: false,
        reason: `API marked as unavailable. Retry in ${Math.ceil(timeUntilRetry / 1000)}s (failures: ${status?.consecutiveFailures || 0})`
      };

      console.log(`[ApiCache] 🚫 Blocking call to ${endpoint}:`, result);
      return result;
    }

    // Mark as pending before allowing the call
    this.markCallPending(endpoint);

    const result = {
      shouldCall: true,
      reason: 'API available or not yet tested'
    };

    console.log(`[ApiCache] ✅ Allowing call to ${endpoint}:`, result);
    return result;
  }
}

// Export singleton instance
export const apiCache = ApiAvailabilityCache.getInstance();

// Export types for external use
export type { ApiStatus };
