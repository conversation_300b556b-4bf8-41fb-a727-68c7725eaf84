
import { useRef, useEffect, useCallback } from 'react';
import mapboxgl from 'mapbox-gl';
import { LocationData } from '@/types/database';
import { useToast } from '@/components/ui/use-toast';

interface UseMapMarkersProps {
  map: mapboxgl.Map | null;
  mapLoaded: boolean;
  locations: LocationData[];
  selectedUserId?: string;
  forceUpdateKey?: number;
}

export function useMapMarkers({
  map,
  mapLoaded,
  locations,
  selectedUserId,
  forceUpdateKey
}: UseMapMarkersProps) {
  const markers = useRef<mapboxgl.Marker[]>([]);
  const { toast } = useToast();
  const retryTimeoutRef = useRef<number | null>(null);

  // Detecta se estamos no dashboard do responsável
  const isParentDashboard = () => {
    return (
      window.location.pathname.includes('parent-dashboard') || 
      window.location.pathname.includes('parent/dashboard') ||
      window.location.pathname.includes('guardian') ||
      document.title.toLowerCase().includes('responsável')
    );
  };

  // Helper function to add markers with retry logic
  const addMarkersToMap = useCallback((retryCount = 0) => {
    if (!map || !mapLoaded || !locations || locations.length === 0) return;

    // Additional safety check: ensure map container is ready
    const mapContainer = map.getContainer();
    if (!mapContainer || !mapContainer.parentNode) {
      if (retryCount < 3) {
        console.warn(`[useMapMarkers] Map container not ready, retrying... (attempt ${retryCount + 1})`);
        retryTimeoutRef.current = setTimeout(() => addMarkersToMap(retryCount + 1), 100);
        return;
      } else {
        console.error('[useMapMarkers] Map container not ready after 3 retries, skipping marker addition');
        return;
      }
    }

    // Clear any existing retry timeout
    if (retryTimeoutRef.current) {
      clearTimeout(retryTimeoutRef.current);
      retryTimeoutRef.current = null;
    }
    
    // Clean up previous markers efficiently
    markers.current.forEach(marker => marker.remove());
    markers.current = [];
    
    const shouldShowAllStudents = isParentDashboard() && !selectedUserId;
    const filteredLocations = shouldShowAllStudents 
      ? locations 
      : (selectedUserId 
          ? locations.filter(loc => loc.user_id === selectedUserId)
          : locations);
    
    console.log('[MapView] Filtered locations:', filteredLocations.length);
    
    // Organize locations by user
    const locationsByUser = new Map<string, LocationData[]>();
    
    filteredLocations.forEach(loc => {
      const userId = loc.user_id;
      if (!locationsByUser.has(userId)) {
        locationsByUser.set(userId, []);
      }
      locationsByUser.get(userId)?.push(loc);
    });
    
    // Sort locations
    locationsByUser.forEach((userLocs, userId) => {
      locationsByUser.set(userId, 
        userLocs.sort((a, b) => 
          new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
        )
      );
    });
    
    const mostRecentLocations: LocationData[] = [];
    
    locationsByUser.forEach((userLocs) => {
      if (userLocs.length > 0) {
        mostRecentLocations.push(userLocs[0]);
      }
    });
    
    // Add markers
    locationsByUser.forEach((userLocs, userId) => {
      userLocs.forEach((location, index) => {
        const isRecentLocation = index === 0;
        
        const markerElement = document.createElement('div');
        markerElement.className = 'custom-marker';
        markerElement.style.cssText = `
          width: ${isRecentLocation ? '30px' : '20px'};
          height: ${isRecentLocation ? '30px' : '20px'};
          border-radius: 50%;
          background-color: ${isRecentLocation ? '#ff0000' : '#888'};
          border: ${isRecentLocation ? '3px solid #ffffff' : '1px solid #ffffff'};
          box-shadow: ${isRecentLocation ? '0 0 10px rgba(255, 0, 0, 0.7)' : 'none'};
        `;
        // Acessibilidade: aria-label
        markerElement.setAttribute('tabindex', '0');
        markerElement.setAttribute('role', 'img');
        markerElement.setAttribute('aria-label', `${location.user?.full_name || 'Localização atual'}: ${new Date(location.timestamp).toLocaleString()}`);
        if (isRecentLocation) {
          const badge = document.createElement('div');
          badge.className = 'location-badge';
          badge.textContent = 'ATUAL';
          badge.style.cssText = `
            position: absolute;
            top: -10px;
            right: -20px;
            background-color: #ff3c00;
            color: white;
            padding: 2px 4px;
            border-radius: 3px;
            font-size: 8px;
            font-weight: bold;
          `;
          badge.setAttribute('aria-label', 'Localização mais recente');
          markerElement.appendChild(badge);
        }
        
        const marker = new mapboxgl.Marker({
          element: markerElement,
          anchor: 'bottom',
        }).setLngLat([location.longitude, location.latitude]);

        const popupContent = `
          <div style="padding: 5px;">
            <h3 style="font-weight: bold; margin-bottom: 5px; font-size: 16px;">
              ${location.user?.full_name || 'Localização'}
              ${isRecentLocation ? '<span style="background-color: #ff3c00; color: white; padding: 2px 5px; border-radius: 3px; font-size: 10px; margin-left: 5px;">ATUAL</span>' : ''}
            </h3>
            <p style="margin-bottom: 3px; font-size: 14px;">${new Date(location.timestamp).toLocaleString()}</p>
            ${location.address ? `<p style="color: #666; font-size: 12px;">${location.address}</p>` : ''}
            ${isRecentLocation ? '<p style="font-weight: bold; color: #ff3c00; margin-top: 5px; border-top: 1px solid #eee; padding-top: 5px;">LOCALIZAÇÃO MAIS RECENTE</p>' : ''}
          </div>
        `;
        
        const popup = new mapboxgl.Popup({ offset: 25 })
          .setHTML(popupContent);
        
        marker.setPopup(popup);

        // Safe marker addition with error handling
        try {
          marker.addTo(map);
          markers.current.push(marker);
        } catch (error) {
          console.error('[useMapMarkers] Failed to add marker to map:', error);
          // Don't add the marker to our tracking array if it failed to add to the map
        }
      });
    });

    // Apply intelligent zoom
    if (mostRecentLocations.length > 0) {
      const parentDashboard = isParentDashboard();
      const parentZoom = 16;
      const regularZoom = 15;
      const parentPadding = 100;
      const regularPadding = 50;

      if (parentDashboard) {
        if (mostRecentLocations.length === 1) {
          const singleLocation = mostRecentLocations[0];
          map.flyTo({
            center: [singleLocation.longitude, singleLocation.latitude],
            zoom: parentZoom + 2,
            essential: true,
            speed: 0.5
          });
        } else {
          // Check if all locations are in the same region
          let allInSameRegion = true;
          const firstLoc = mostRecentLocations[0];
          
          for (let i = 1; i < mostRecentLocations.length; i++) {
            const loc = mostRecentLocations[i];
            const latDiff = Math.abs(loc.latitude - firstLoc.latitude);
            const lngDiff = Math.abs(loc.longitude - firstLoc.longitude);
            
            if (latDiff > 5 || lngDiff > 5) {
              allInSameRegion = false;
              break;
            }
          }
          
          if (allInSameRegion) {
            const bounds = new mapboxgl.LngLatBounds();
            mostRecentLocations.forEach(loc => {
              bounds.extend([loc.longitude, loc.latitude]);
            });
            
            map.fitBounds(bounds, {
              padding: parentPadding,
              maxZoom: parentZoom
            });
          } else {
            const sortedLocations = [...mostRecentLocations].sort((a, b) => 
              new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
            );
            
            const absoluteLatestLocation = sortedLocations[0];
            map.flyTo({
              center: [absoluteLatestLocation.longitude, absoluteLatestLocation.latitude],
              zoom: parentZoom,
              essential: true,
              speed: 0.5
            });
            
            toast({
              title: "Localização mais recente em foco",
              description: `Mostrando a localização mais recente de ${absoluteLatestLocation.user?.full_name}. Use os botões para ver outras localizações.`,
              duration: 5000
            });
          }
        }
      } else {
        const bounds = new mapboxgl.LngLatBounds();
        locations.forEach(location => {
          bounds.extend([location.longitude, location.latitude]);
        });
        
        map.fitBounds(bounds, {
          padding: regularPadding,
          maxZoom: regularZoom
        });
      }
    }
  }, [map, mapLoaded, locations, selectedUserId, toast]);

  // Update markers when locations change
  useEffect(() => {
    addMarkersToMap();

    // Cleanup function
    return () => {
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current);
        retryTimeoutRef.current = null;
      }
    };
  }, [addMarkersToMap, forceUpdateKey]);

  return { markers: markers.current };
}
